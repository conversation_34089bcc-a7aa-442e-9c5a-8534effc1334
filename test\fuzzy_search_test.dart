import 'package:flutter_test/flutter_test.dart';
import 'package:fuzzywuzzy/fuzzywuzzy.dart';

void main() {
  group('Fuzzy Search Tests', () {
    test('Test fuzzy matching for Serbian word variations', () {
      // Test case: searching for "Srbija" should match "<PERSON><PERSON><PERSON>", "Srbije", etc.
      String searchTerm = 'srbija';
      List<String> testItems = [
        'Srbija',
        'Srbiji',
        'Srbije',
        'Srbijom',
        'Srbija<PERSON>',
        'Istorija Srbije',
        'Kultura Srbije',
        'Geografija Srbije',
        'Politika Srbije',
        'Ekonomija Srbije',
        'Nemačka', // Should not match
        'Fran<PERSON><PERSON>', // Should not match
      ];

      print('Testing fuzzy search for: "$searchTerm"');
      print('=' * 50);

      for (String item in testItems) {
        int score = ratio(searchTerm.toLowerCase(), item.toLowerCase());
        int partialScore =
            partialRatio(searchTerm.toLowerCase(), item.toLowerCase());
        int tokenSortScore =
            tokenSortRatio(searchTerm.toLowerCase(), item.toLowerCase());
        int tokenSetScore =
            tokenSetRatio(searchTerm.toLowerCase(), item.toLowerCase());

        int finalScore = [score, partialScore, tokenSortScore, tokenSetScore]
            .reduce((a, b) => a > b ? a : b);

        bool wouldMatch = finalScore >= 60;

        print('Item: "$item"');
        print(
            '  Scores: ratio=$score, partial=$partialScore, tokenSort=$tokenSortScore, tokenSet=$tokenSetScore');
        print('  Final Score: $finalScore');
        print('  Would Match (≥60%): $wouldMatch');
        print('');
      }

      // Verify that Serbian variations match
      expect(ratio('srbija', 'srbiji') >= 60, true);
      expect(partialRatio('srbija', 'istorija srbije') >= 60, true);
      expect(partialRatio('srbija', 'kultura srbije') >= 60, true);
    });

    test('Test exact matches still work', () {
      String searchTerm = 'matematika';
      String exactMatch = 'Matematika';

      int score = ratio(searchTerm.toLowerCase(), exactMatch.toLowerCase());

      // Exact matches should have 100% score
      expect(score, 100);
    });

    test('Test partial word matching', () {
      String searchTerm = 'srb';
      String fullWord = 'Srbija';

      int partialScore =
          partialRatio(searchTerm.toLowerCase(), fullWord.toLowerCase());

      // Partial matches should have high scores
      expect(partialScore >= 60, true);
    });
  });
}

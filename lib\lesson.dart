import 'dart:async';
import 'dart:io';

import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/models/lesson_model.dart';
import 'package:bibl/models/page_model.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/views/completion.dart';
import 'package:bibl/widgets/arrow_back_button_widget.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:bibl/widgets/question_widget.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'controllers/analytics_controller.dart';
import 'controllers/lesson_controller.dart';
import 'services/reward_service.dart';
import 'widgets/customappbar.dart';
import 'widgets/local_image_widget.dart';

class Lesson extends StatefulWidget {
  final LessonModel lesson;
  const Lesson({Key? key, required this.lesson}) : super(key: key);

  @override
  State<Lesson> createState() => _LessonState();
}

class _LessonState extends State<Lesson> {
  final PageController _pageController = PageController();
  final ProfileController profileController = Get.find();
  final LessonController lessonController = Get.find();
  final AnalticsController analticsController = AnalticsController();

  int currentPage = 0;
  int obtainedMarks = 0;
  List<String?> selectedOptions = [];
  List<int?> selectedOptionsIndexes = [];

  int readingSeconds = 0;
  late Timer readingTimer;
  bool timerRunning = false;

  void startReadingTimer() {
    timerRunning = true;
    readingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      readingSeconds++;
      if (readingSeconds >= 1800) {
        rewardAchievement();
        stopReadingTimer();
      }
    });
  }

  void stopReadingTimer() {
    if (timerRunning) {
      readingTimer.cancel();
      timerRunning = false;
    }
  }

  bool isEligibleForReward(Timestamp? lastTimestamp) {
    if (lastTimestamp == null) return true; // Eligible if no timestamp exists
    final currentTime = DateTime.now();
    final lastRewardTime = lastTimestamp.toDate();
    final difference = currentTime.difference(lastRewardTime);
    return difference.inDays >= 7;
  }

  void rewardAchievement() async {
    final lastRewardTimestamp =
        profileController.userr.value.lastRewardLessonMarathonerTimestamp;

    if (isEligibleForReward(lastRewardTimestamp)) {
      if (profileController.userr.value.uid != null) {
        // Initialize the RewardService with the user's UID
        RewardService rewardService =
            RewardService(profileController.userr.value.uid!);
        rewardService.marathoner();
        updateLastRewardTimestamp(profileController.userr.value.uid!);
      }
    }
  }

  Future<void> updateLastRewardTimestamp(String uid) async {
    final currentTime = Timestamp.now();
    await firestore
        .collection('users')
        .doc(uid)
        .update({'lastRewardLessonMarathonerTimestamp': currentTime});
  }

  @override
  void initState() {
    super.initState();

    startReadingTimer();
    analticsController.lessonStartedAnalyticsUpdate(widget.lesson.lessonName!);

    // Initialize selectedOptions and selectedOptionsIndexes with nulls for all pages
    selectedOptions = List<String?>.filled(widget.lesson.pages!.length, null);
    selectedOptionsIndexes =
        List<int?>.filled(widget.lesson.pages!.length, null);
  }

  final bool _isGoingBack = false;

  @override
  void dispose() {
    _pageController.dispose();
    // player.dispose();
    stopReadingTimer(); // Ensure timer stops when exiting the page
    super.dispose();
  }

  void _goToPreviousPage() {
    if (_isGoingBack) return;

    if (currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToNextPage() {
    if (currentPage < widget.lesson.pages!.length - 1) {
      // Check if the selected option is correct and  update obtained marks
      if (selectedOptions[currentPage] ==
          widget.lesson.pages![currentPage].quiz!.correctOption) {
        obtainedMarks++;
      }

      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      calculateNeuronsPoints();
    }
  }

  void calculateNeuronsPoints() {
    int totalEarnedPoints = 0;

    totalEarnedPoints =
        calculatePoints(widget.lesson.pages, selectedOptions, 10);
    Get.off(() => Completion(
          id: widget.lesson.lessonId!,
          earnedNeurons: totalEarnedPoints,
          isFromLesson: true,
        ));
  }

  int calculatePoints(
      List<PageModel>? pages, List? answers, int pointsPerQuestion) {
    if (pages == null || answers == null) return 0;

    int correctAnswers = 0;
    for (int i = 0; i < pages.length - 1; i++) {
      if (answers[i] == pages[i].quiz!.correctOption!) {
        correctAnswers++;
      }
    }
    return correctAnswers * pointsPerQuestion;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        _goToPreviousPage();
      },
      child: SafeArea(
          bottom: true,
          top: false,
          child: Scaffold(
              appBar: CustomAppBar(
                widget: Row(
                  children: [
                    const SizedBox(
                      width: 10,
                    ),
                    arrowBackButtonWidget(),
                    const SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      child: LinearProgressIndicator(
                        borderRadius: BorderRadius.circular(16),
                        minHeight: 6,
                        value: (currentPage + 1) / widget.lesson.pages!.length,
                        backgroundColor: Colors.white.withValues(alpha: 0.2),
                        valueColor:
                            const AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    const SizedBox(
                      width: 30,
                    ),
                  ],
                ),
              ),
              body: PageView.builder(
                physics: const NeverScrollableScrollPhysics(),
                controller: _pageController,
                onPageChanged: (page) {
                  setState(() {
                    if (page >= 0 && page < widget.lesson.pages!.length) {
                      currentPage = page;
                    }
                  });
                },
                itemCount: widget.lesson.pages != null
                    ? widget.lesson.pages!.length
                    : 0,
                itemBuilder: (context, index) {
                  return widget.lesson.pages!.length > 1
                      ? currentPage == widget.lesson.pages!.length - 1
                          ? lessonLastPageWidget(widget.lesson.pages![index])
                          : lessonPageWidget(widget.lesson.pages![index])
                      : lessonPageWidget(widget.lesson.pages![index]);
                },
              ))),
    );
  }

  Stack lessonPageWidget(PageModel page) {
    return Stack(
      children: [
        Positioned.fill(
          child: Column(
            children: [
              Expanded(
                child: ScrollConfiguration(
                  behavior: const ScrollBehavior(),
                  child: ListView(
                    padding: const EdgeInsets.all(16),
                    children: [
                      SizedBox(
                        height: 220,
                        child: FutureBuilder<File?>(
                          future: lessonController.fetchAndCacheImage(
                              page.pagePhotoLink!,
                              "${widget.lesson.lessonId}_${page.pageNo}.jpg"),
                          builder: (context, snapshot) {
                            return LocalImageWidget(imageFile: snapshot.data);
                          },
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Row(
                        children: [
                          Flexible(
                            child: Txt(
                              txt: page.pageTitle!,
                              maxLines: 5,
                              fontSize: 20,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Txt(
                        txt: page.pageContent!,
                        maxLines: 1000,
                        fontSize: 14,
                        fontColor: grey2Color,
                        fontWeight: FontWeight.normal,
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                      page.quiz != null
                          ? QuestionWidget(
                              question: page.quiz!,
                              selectedOptionIndexx:
                                  selectedOptionsIndexes[currentPage],
                              isAnswered: selectedOptions[currentPage] != null
                                  ? true
                                  : false,
                              onOptionSelected: (
                                  {required option, required selectedIndex}) {
                                selectedOptions[currentPage] = option;
                                selectedOptionsIndexes[currentPage] =
                                    selectedIndex;

                                setState(() {});
                              },
                            )
                          : const SizedBox.shrink(),
                      buttonContainer(
                        text: 'Nastavi',
                        onTap: currentPage == widget.lesson.pages!.length - 1
                            ? _goToNextPage
                            : selectedOptions.length <= currentPage ||
                                    selectedOptions[currentPage] == null
                                ? null
                                : _goToNextPage,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 20,
        )
      ],
    );
  }

  Stack lessonLastPageWidget(PageModel page) {
    return Stack(
      children: [
        Positioned.fill(
          child: ListView(
            children: [
              Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                          color: Colors.black.withValues(alpha: 0.2)),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        children: [
                          SizedBox(
                            height: 220,
                            child: FutureBuilder<File?>(
                              future: lessonController.fetchAndCacheImage(
                                  page.pagePhotoLink!,
                                  "${widget.lesson.lessonId}_${page.pageNo}.jpg"),
                              builder: (context, snapshot) {
                                return LocalImageWidget(
                                    imageFile: snapshot.data);
                              },
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Flexible(
                                child: Txt(
                                  txt: page.pageTitle!,
                                  maxLines: 5,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          ListView.builder(
                            shrinkWrap: true,
                            padding: EdgeInsets.zero,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: page.pageContent!.split('  ').length,
                            itemBuilder: (context, index) {
                              String line =
                                  page.pageContent!.split('  ')[index];
                              return Column(
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Padding(
                                        padding: EdgeInsets.all(3.0),
                                        child: Icon(
                                          Icons.check_circle,
                                          color: Color(0xff7CE099),
                                          size: 16,
                                        ),
                                      ),
                                      const SizedBox(
                                          width:
                                              8), // Adjust the spacing between the icon and text
                                      Expanded(
                                        child: Txt(
                                          txt: line,
                                          maxLines: 1000,
                                          fontSize: 14,
                                          fontWeight: FontWeight.normal,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 16,
                                  )
                                ],
                              );
                            },
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          page.quiz != null
                              ? QuestionWidget(
                                  question: page.quiz!,
                                  selectedOptionIndexx:
                                      selectedOptionsIndexes[currentPage],
                                  isAnswered:
                                      selectedOptions[currentPage] != null
                                          ? true
                                          : false,
                                  onOptionSelected: (
                                      {required option,
                                      required selectedIndex}) {
                                    selectedOptions[currentPage] = option;
                                    selectedOptionsIndexes[currentPage] =
                                        selectedIndex;

                                    setState(() {});
                                  },
                                )
                              : const SizedBox.shrink(),
                        ],
                      ),
                    ),
                  )),
              const SizedBox(height: 100.0),
            ],
          ),
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: buttonContainer(
              text: 'Nastavi',
              onTap: currentPage == widget.lesson.pages!.length - 1
                  ? _goToNextPage
                  : selectedOptions.length <= currentPage ||
                          selectedOptions[currentPage] == null
                      ? null
                      : _goToNextPage,
            ),
          ),
        )
      ],
    );
  }
}

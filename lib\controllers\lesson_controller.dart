import 'dart:io';

import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/controllers/quiz_controller.dart';
import 'package:bibl/models/lesson_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/quiz_model.dart';
import '../res/style.dart';
import '../services/db_service.dart';
import '../services/shared_preferences.dart';

class LessonController extends GetxController {
  final Map<String, Future<File?>> _imageCache = {};

  RxList<dynamic> mergedItems = <dynamic>[].obs;
  RxList<dynamic> allItems = <dynamic>[].obs;

  int homeItemsPerPage = 3;
  RxInt currentPageForHomeItems = 1.obs;
  RxList<dynamic> homeDisplayedItems = <dynamic>[].obs;
  int libraryItemsPerPage = 3;
  RxInt currentPageForLibraryItems = 1.obs;
  RxList<dynamic> libraryDisplayedItems = <dynamic>[].obs;

  DocumentSnapshot? _lastDoc;
  bool _hasMoreData = true;
  final int _chunkSize = 10;

  final DatabaseHelper dbHelper = DatabaseHelper();
  RxList<LessonModel> allLessons = <LessonModel>[].obs;
  final CacheManager _cacheManager = CacheManager(
    Config(
      'images_cache',
      stalePeriod: const Duration(days: 7), // Revert to stable duration
      maxNrOfCacheObjects: 1000, // Revert to stable size
    ),
  );

  CacheManager get cacheManager => _cacheManager;

  @override
  void onInit() {
    super.onInit();
    loadContent();
    preloadInitialImages(); // Preload images for initial screen
  }

  // Aggressive image preloading for super-fast display
  Future<void> startAggressiveImagePreloading() async {
    try {
      // Load from local database first for immediate display
      final localLessons = await dbHelper.getLessons();
      if (localLessons.isNotEmpty) {
        allLessons.assignAll(localLessons);

        // Start preloading images from local data immediately
        await _preloadImagesFromLocalData();
      }
    } catch (e) {
      debugPrint('Error in aggressive image preloading: $e');
    }
  }

  // Preload images from locally cached data for instant display
  Future<void> _preloadImagesFromLocalData() async {
    // Preload first 20 items for immediate display
    final priorityItems = allLessons.take(20).toList();

    for (var lesson in priorityItems) {
      for (var page in lesson.pages ?? []) {
        if (page.pagePhotoLink != null && page.pagePhotoLink!.isNotEmpty) {
          try {
            await _cacheManager.getSingleFile(page.pagePhotoLink!);
          } catch (e) {
            debugPrint('Error preloading lesson image: $e');
          }
        }
      }
    }

    // Continue preloading remaining images in background
    _preloadRemainingImagesInBackground();
  }

  // Preload remaining images in background without blocking UI
  void _preloadRemainingImagesInBackground() {
    Future.microtask(() async {
      final remainingLessons = allLessons.skip(20).toList();

      for (var lesson in remainingLessons) {
        for (var page in lesson.pages ?? []) {
          if (page.pagePhotoLink != null && page.pagePhotoLink!.isNotEmpty) {
            try {
              await _cacheManager.getSingleFile(page.pagePhotoLink!);
            } catch (e) {
              debugPrint('Error preloading background lesson image: $e');
            }
          }
        }
      }

      debugPrint('Background lesson image preloading completed');
    });
  }

  Future<void> preloadInitialImages() async {
    debugPrint('Preloading images for initial screen');
    final priorityItems = mergedItems.take(homeItemsPerPage).toList();
    final futures = <Future>[];

    for (var item in priorityItems) {
      if (item is LessonModel) {
        for (var page in item.pages ?? []) {
          if (page.pagePhotoLink != null && page.pagePhotoLink!.isNotEmpty) {
            futures.add(fetchAndCacheImage(
              page.pagePhotoLink!,
              '${item.lessonId}_${page.pageNo}.jpg',
            ));
          }
        }
      } else if (item is QuizModel) {
        if (item.quizImageLink != null && item.quizImageLink!.isNotEmpty) {
          futures.add(fetchAndCacheImage(
            item.quizImageLink!,
            '${item.quizId}_image.jpg',
          ));
        }
      } else if (item is ShuffleQuizModel) {
        for (var question in item.questionsList ?? []) {
          if (question.qsImage != null && question.qsImage!.isNotEmpty) {
            futures.add(fetchAndCacheImage(
              question.qsImage!,
              '${item.quizId}_${question.qsNo}.jpg',
            ));
          }
        }
      }
    }

    await Future.wait(futures).catchError((e) {
      debugPrint('Error in preloading initial images: $e');
      return <dynamic>[];
    });

    // Preload remaining images in background
    final remainingItems = mergedItems.skip(homeItemsPerPage).toList();
    for (var item in remainingItems) {
      if (item is LessonModel) {
        for (var page in item.pages ?? []) {
          if (page.pagePhotoLink != null && page.pagePhotoLink!.isNotEmpty) {
            fetchAndCacheImage(
              page.pagePhotoLink!,
              '${item.lessonId}_${page.pageNo}.jpg',
            ).catchError((e) {
              debugPrint('Error preloading background image: $e');
              return null;
            });
          }
        }
      } else if (item is QuizModel) {
        if (item.quizImageLink != null && item.quizImageLink!.isNotEmpty) {
          fetchAndCacheImage(
            item.quizImageLink!,
            '${item.quizId}_image.jpg',
          ).catchError((e) {
            debugPrint('Error preloading background image: $e');
            return null;
          });
        }
      } else if (item is ShuffleQuizModel) {
        for (var question in item.questionsList ?? []) {
          if (question.qsImage != null && question.qsImage!.isNotEmpty) {
            fetchAndCacheImage(
              question.qsImage!,
              '${item.quizId}_${question.qsNo}.jpg',
            ).catchError((e) {
              debugPrint('Error preloading background image: $e');
              return null;
            });
          }
        }
      }
    }
  }

  Future<void> prefetchImages(List<String> urls, String prefix) async {
    final futures = urls.asMap().entries.map((entry) {
      final url = entry.value;
      final fileName = '$prefix${entry.key}.jpg';
      debugPrint('Prefetching image: $url');
      return fetchAndCacheImage(url, fileName).then((file) {
        debugPrint('Prefetched $url');
        return file;
      }).catchError((e) {
        debugPrint('Error prefetching $url: $e');
        return null;
      });
    }).toList();
    await Future.wait(futures);
  }

  Future<File?> fetchAndCacheImage(String imageUrl, String fileName,
      {bool? isnew}) async {
    final key = '$imageUrl$fileName';
    if (!_imageCache.containsKey(key) || isnew == true) {
      _imageCache[key] = _cacheManager.getSingleFile(imageUrl).catchError((e) {
        debugPrint('Error fetching image $imageUrl: $e');
        throw e; // Re-throw to maintain proper error handling
      });
    }
    return _imageCache[key];
  }

  List<dynamic> reorderItems(List<dynamic> items) {
    List<dynamic> articles = items.whereType<LessonModel>().toList();
    List<dynamic> shuffleQuizzes = items.whereType<ShuffleQuizModel>().toList();
    List<dynamic> normalQuizzes = items.whereType<QuizModel>().toList();

    List<dynamic> reorderedItems = [];
    int articleIndex = 0, shuffleIndex = 0, normalIndex = 0;

    while (articleIndex < articles.length ||
        shuffleIndex < shuffleQuizzes.length ||
        normalIndex < normalQuizzes.length) {
      for (int i = 0; i < 3 && articleIndex < articles.length; i++) {
        reorderedItems.add(articles[articleIndex++]);
      }
      if (shuffleIndex < shuffleQuizzes.length) {
        reorderedItems.add(shuffleQuizzes[shuffleIndex++]);
      }
      if (normalIndex < normalQuizzes.length) {
        reorderedItems.add(normalQuizzes[normalIndex++]);
      }
    }
    return reorderedItems;
  }

  void loadInitialMergedItems() {
    homeDisplayedItems.clear();
    final initialChunk = mergedItems.take(homeItemsPerPage).toList();
    homeDisplayedItems.addAll(initialChunk);
    currentPageForHomeItems.value = 1;
  }

  void loadInitialAllItems() {
    libraryDisplayedItems.clear();
    final initialChunk = allItems.take(libraryItemsPerPage).toList();
    libraryDisplayedItems.addAll(initialChunk);
    currentPageForLibraryItems.value = 1;
  }

  void loadMoreHomeDisplayItems() {
    final startIndex = homeDisplayedItems.length;
    if (startIndex >= mergedItems.length) return;
    final nextChunk =
        mergedItems.skip(startIndex).take(homeItemsPerPage).toList();
    homeDisplayedItems.addAll(nextChunk);
    currentPageForHomeItems.value++;
  }

  void loadMoreLibraryDisplayItems() {
    final startIndex = libraryDisplayedItems.length;
    if (startIndex >= allItems.length) return;
    final nextChunk =
        allItems.skip(startIndex).take(libraryItemsPerPage).toList();
    libraryDisplayedItems.addAll(nextChunk);
    currentPageForLibraryItems.value++;
  }

  loadContent() async {
    final QuizController quizController = Get.find();
    await Future.wait([
      loadLessons(),
      quizController.loadQuizzes(),
      quizController.loadShuffleQuizzes(),
    ]);
    if (homeDisplayedItems.isEmpty) {
      mergeAndShuffleItems(isShuffle: true, shouldClear: true, from: 'load');
    }
    if (libraryDisplayedItems.isEmpty) {
      shuffleAllItems(isShuffle: true, from: 'load', shouldClear: true);
    }
  }

  void mergeAndShuffleItems({
    required bool isShuffle,
    required bool shouldClear,
    required String from,
    List<ShuffleQuizModel>? fetchedShuffleQuizzes,
    List<QuizModel>? fetchedQuizzes,
    List<LessonModel>? fetchedLessons,
  }) {
    if (!Get.isRegistered<ProfileController>()) return;
    final QuizController quizController = Get.find();

    List<LessonModel> filteredLessons = _filterItemsByCategory(allLessons);
    List<ShuffleQuizModel> filteredShuffleQuizzes =
        _filterItemsByCategory(quizController.allShuffleQuizzes);
    List<QuizModel> filteredQuizzes =
        _filterItemsByCategory(quizController.allQuizzes);

    if (shouldClear) mergedItems.clear();

    if (fetchedShuffleQuizzes != null) {
      for (var fetchedQuiz in fetchedShuffleQuizzes) {
        mergedItems.removeWhere((item) =>
            item is ShuffleQuizModel && item.quizId == fetchedQuiz.quizId);
        mergedItems.add(fetchedQuiz);
      }
    }
    if (fetchedQuizzes != null) {
      for (var fetchedQuiz in fetchedQuizzes) {
        mergedItems.removeWhere(
            (item) => item is QuizModel && item.quizId == fetchedQuiz.quizId);
        mergedItems.add(fetchedQuiz);
      }
    }
    if (fetchedLessons != null) {
      for (var fetchedLesson in fetchedLessons) {
        mergedItems.removeWhere((item) =>
            item is LessonModel && item.lessonId == fetchedLesson.lessonId);
        mergedItems.add(fetchedLesson);
      }
    }

    for (var lesson in filteredLessons) {
      if (!mergedItems.any(
          (item) => item is LessonModel && item.lessonId == lesson.lessonId)) {
        mergedItems.add(lesson);
      }
    }
    for (var shuffleQuiz in filteredShuffleQuizzes) {
      if (!mergedItems.any((item) =>
          item is ShuffleQuizModel && item.quizId == shuffleQuiz.quizId)) {
        mergedItems.add(shuffleQuiz);
      }
    }
    for (var quiz in filteredQuizzes) {
      if (!mergedItems
          .any((item) => item is QuizModel && item.quizId == quiz.quizId)) {
        mergedItems.add(quiz);
      }
    }

    mergedItems.assignAll(reorderItems(mergedItems));
    if (isShuffle) mergedItems.shuffle();
    loadInitialMergedItems();
  }

  void shuffleAllItems({
    required bool isShuffle,
    required String from,
    required bool shouldClear,
    List<ShuffleQuizModel>? fetchedShuffleQuizzes,
    List<QuizModel>? fetchedQuizzes,
    List<LessonModel>? fetchedLessons,
  }) {
    if (!Get.isRegistered<ProfileController>()) return;
    final QuizController quizController = Get.find();

    List<LessonModel> filteredLessons =
        _filterLibraryItemsByCategory(allLessons);
    List<ShuffleQuizModel> filteredShuffleQuizzes =
        _filterLibraryItemsByCategory(quizController.allShuffleQuizzes);
    List<QuizModel> filteredQuizzes =
        _filterLibraryItemsByCategory(quizController.allQuizzes);

    if (shouldClear) allItems.clear();

    if (fetchedShuffleQuizzes != null) {
      for (var fetchedQuiz in fetchedShuffleQuizzes) {
        allItems.removeWhere((item) =>
            item is ShuffleQuizModel && item.quizId == fetchedQuiz.quizId);
        allItems.add(fetchedQuiz);
      }
    }
    if (fetchedQuizzes != null) {
      for (var fetchedQuiz in fetchedQuizzes) {
        allItems.removeWhere(
            (item) => item is QuizModel && item.quizId == fetchedQuiz.quizId);
        allItems.add(fetchedQuiz);
      }
    }
    if (fetchedLessons != null) {
      for (var fetchedLesson in fetchedLessons) {
        allItems.removeWhere((item) =>
            item is LessonModel && item.lessonId == fetchedLesson.lessonId);
        allItems.add(fetchedLesson);
      }
    }

    for (var lesson in filteredLessons) {
      if (!allItems.any(
          (item) => item is LessonModel && item.lessonId == lesson.lessonId)) {
        allItems.add(lesson);
      }
    }
    for (var shuffleQuiz in filteredShuffleQuizzes) {
      if (!allItems.any((item) =>
          item is ShuffleQuizModel && item.quizId == shuffleQuiz.quizId)) {
        allItems.add(shuffleQuiz);
      }
    }
    for (var quiz in filteredQuizzes) {
      if (!allItems
          .any((item) => item is QuizModel && item.quizId == quiz.quizId)) {
        allItems.add(quiz);
      }
    }

    allItems.assignAll(reorderItems(allItems));
    if (isShuffle) allItems.shuffle();
    loadInitialAllItems();
  }

  List<T> _filterItemsByCategory<T>(List<T> items) {
    if (!Get.isRegistered<ProfileController>()) return items;
    ProfileController profileController = Get.find();
    final favCategories = profileController.userr.value.listOfFavCategories;
    if (favCategories == null || favCategories.isEmpty) return items;

    return items.where((item) {
      String? category;
      if (item is LessonModel) {
        category = item.category;
      } else if (item is ShuffleQuizModel) {
        category = item.category;
      } else if (item is QuizModel) {
        category = item.category;
      } else {
        return false;
      }
      return favCategories.contains(category);
    }).toList();
  }

  List<T> _filterLibraryItemsByCategory<T>(List<T> items) {
    if (!Get.isRegistered<ProfileController>()) return items;
    ProfileController profileController = Get.find();
    final favCategories = profileController.userr.value.listOfLibraryCategories;
    if (favCategories == null || favCategories.isEmpty) return items;

    return items.where((item) {
      String? category;
      if (item is LessonModel) {
        category = item.category;
      } else if (item is ShuffleQuizModel) {
        category = item.category;
      } else if (item is QuizModel) {
        category = item.category;
      } else {
        return false;
      }
      return favCategories.contains(category);
    }).toList();
  }

  Future<void> loadLessons() async {
    try {
      final lessons = await dbHelper.getLessons();
      if (lessons.isNotEmpty) {
        allLessons.assignAll(lessons);
      }
      bool shouldUpdate = await shouldFetchFromFirestore();
      if (shouldUpdate) {
        await fetchLessonsFromFirestore();
      }
    } catch (e) {
      debugPrint('Error loading lessons: $e');
    }
  }

  Future<bool> shouldFetchFromFirestore() async {
    final lastSyncTime = SharedPrefs.getData(key: 'lastSyncTime') ?? 0;
    try {
      final latestFirestoreSnapshot = await firestore
          .collection('lessons')
          .orderBy('lastUpdated', descending: true)
          .limit(1)
          .get();
      if (latestFirestoreSnapshot.docs.isNotEmpty) {
        final firestoreLatestTimestamp =
            (latestFirestoreSnapshot.docs.first['lastUpdated'] as Timestamp)
                .millisecondsSinceEpoch;
        return firestoreLatestTimestamp > lastSyncTime;
      }
      return true;
    } catch (e) {
      return true;
    }
  }

  Future<void> fetchLessonsFromFirestore() async {
    final lastSyncTime = SharedPrefs.getData(key: 'lastSyncTime') ?? 0;
    try {
      _hasMoreData = true;
      _lastDoc = null;
      while (_hasMoreData) {
        await _fetchOneChunk(lastSyncTime);
      }
      await SharedPrefs.setData(
        key: 'lastSyncTime',
        value: DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      debugPrint('Error fetching lessons: $e');
    }
  }

  Future<void> _fetchOneChunk(int lastSyncTime) async {
    Query query = firestore
        .collection('lessons')
        .where('lastUpdated',
            isGreaterThan: Timestamp.fromMillisecondsSinceEpoch(lastSyncTime))
        .orderBy('lastUpdated', descending: false)
        .limit(_chunkSize);

    if (_lastDoc != null) {
      query = query.startAfterDocument(_lastDoc!);
    }

    final snapshot = await query.get();
    if (snapshot.docs.isEmpty) {
      _hasMoreData = false;
      return;
    }

    List<LessonModel> fetchedLessons = [];
    for (var doc in snapshot.docs) {
      try {
        final lesson = LessonModel.fromJson(doc.data() as Map<String, dynamic>);
        fetchedLessons.add(lesson);
        await dbHelper.insertLesson(lesson);
        allLessons.add(lesson);
      } catch (err) {
        debugPrint('Error processing lesson: $err');
      }
    }

    _lastDoc = snapshot.docs.last;
    mergeAndShuffleItems(
        fetchedLessons: fetchedLessons,
        isShuffle: false,
        from: 'lesson chunk',
        shouldClear: false);
    shuffleAllItems(
        fetchedLessons: fetchedLessons,
        isShuffle: false,
        from: 'lesson chunk shuffle all',
        shouldClear: false);

    if (snapshot.docs.length < _chunkSize) {
      _hasMoreData = false;
    }
  }
}

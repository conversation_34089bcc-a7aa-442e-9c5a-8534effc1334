import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/models/league_user_model.dart';
import 'package:bibl/res/style.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../widgets/achivement_dialog.dart';

class RewardService {
  final String uid; // pass the user UID here

  RewardService(this.uid);

  /// Helper method to safely show dialogs after build is complete
  void _showAchievementDialog(String image, String message) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Get.dialog(AchievementDialog(
        image: image,
        message: message,
      ));
    });
  }

  /// Update the user's achievements list
  Future<void> _updateAchievements(String badgeId) async {
    final ProfileController profileController = Get.find();
    var userRef = firestore.collection('users').doc(uid);

    // Fetch the current achievements from Firestore
    var userDoc = await userRef.get();
    if (userDoc.exists) {
      List<dynamic> achievements = userDoc.data()?['achievements'] ?? [];
      int initialLength = achievements.length; // Track the initial length

      // Add the new badge if it doesn't already exist
      if (!achievements.contains(badgeId)) {
        await userRef.update({
          'achievements': FieldValue.arrayUnion([badgeId]),
        });
        achievements.add(badgeId); // Simulate the addition in the local list
      }

      // Check neuron-related achievements
      if (profileController.userr.value.neurons != null) {
        if (profileController.userr.value.neurons! > 1000 &&
            !achievements.contains('point_collector')) {
          pointCollector();
        }
        if (profileController.userr.value.neurons! > 5000 &&
            !achievements.contains('point_hoarder')) {
          pointHoarder();
        }
      }

      // Check for ultimateLearner achievement
      if (initialLength == 48 && achievements.length == 49) {
        ultimateLearner();
      }
    }
  }

  Future<void> _updateNeurons(int neurons) async {
    ProfileController profileController = Get.find();
    profileController.userr.value.neurons =
        neurons + profileController.userr.value.neurons!;
    WriteBatch batch = firestore.batch();

    DocumentReference userDoc = firestore.collection('users').doc(uid);

    // Add the updates to the batch
    batch.update(userDoc, {'neurons': FieldValue.increment(neurons)});

    profileController.userr.refresh();
    // Commit the batch
    await batch.commit();
  }

  Future<void> _updateLeaderboard(int score) async {
    final profileController = Get.find<ProfileController>();
    final firestore = FirebaseFirestore.instance;
    final authUser = FirebaseAuth.instance.currentUser;

    if (authUser == null) {
      return;
    }

    final userId = profileController.userr.value.uid;
    final league = profileController.userr.value.league ?? '';
    final groupId = profileController.userr.value.groupId ?? '';
    if (league.isEmpty) {
      return;
    }

    final playerDocRef = firestore
        .collection('leaderboards')
        .doc(league)
        .collection('groups')
        .doc(groupId)
        .collection('players')
        .doc(userId);

    try {
      final playerDoc = await playerDocRef.get();

      if (!playerDoc.exists) {
        print('Creating new player document');

        final username =
            profileController.userr.value.uniqueName?.isNotEmpty == true
                ? profileController.userr.value.uniqueName!
                : authUser.displayName ?? 'Unknown';

        final newPlayer = LeaguePlayerModel(
          username: username,
          league: league,
          score: score,
          playerId: userId,
          lastUpdated: Timestamp.now(),
        );

        await playerDocRef.set(newPlayer.toJson());
      } else {
        print('Updating existing player document');

        final playerData = LeaguePlayerModel.fromSnap(playerDoc);
        final updatedScore = (playerData.score ?? 0) + score;

        await playerDocRef
            .update({'score': updatedScore, 'lastUpdated': Timestamp.now()});
      }
    } catch (e) {
      print('Error updating leaderboard: $e');
      //
    }
  }

  Future<void> addNeurons(int neurons) async {
    // Run all updates in parallel
    await Future.wait([_updateNeurons(neurons), _updateLeaderboard(neurons)]);
  }

  Future<void> firstSteps() async {
    int neurons = 50;

    // Run all updates in parallel
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('first_steps'),
      firestore.collection('users').doc(uid).update({
        'firstQuizComplete': true,
      }),
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a1', 'Prvi koraci! Završili ste svoj prvi kviz.');
  }

  Future<void> quizWiz() async {
    int neurons = 150;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('quiz_wiz')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a2', 'Majstor kvizova! Završite 10 kvizova.');
  }

  Future<void> topicExplorer() async {
    int neurons = 50;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('topic_explorer')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a3', 'Istraživač! Otvorili ste svoju prvu temu!');
  }

  Future<void> dailyLearner() async {
    int neurons = 100;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('daily_learner')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a4', 'Upornost! Prijavili ste se 3 uzastopna dana.');
  }

  Future<void> weeklyStreak() async {
    int neurons = 200;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('weekly_streak')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a5', 'Nedeljni niz! Prijavili ste se 7 uzastopnih dana!');
  }

  Future<void> noviceReader() async {
    int neurons = 100;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('novice_reader')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a6', 'Početnik čitalac! Pročitali ste 5 članaka.');
  }

  Future<void> intermediateReader() async {
    int neurons = 150;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('intermediate_reader')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a7', ' Čitalac! Pročitali ste 10 članaka.');
  }

  Future<void> advancedReader() async {
    int neurons = 200;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('advanced_reader')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a8', 'Napredni Čitalac! Pročitali ste 20 članaka.');
  }

  Future<void> firstCorrectAnswer() async {
    int neurons = 10;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('first_correct_answer')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a9', 'Dobili ste svoj prvi tačan odgovor na kvizu!');
  }

  Future<void> accuracyAce() async {
    int neurons = 50;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('accuracy_ace')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a10', 'As preciznosti! Postigli te 90% tačnosti na kvizu.');
  }

  Future<void> pointCollector() async {
    int neurons = 150;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('point_collector')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a11', 'Sakupljač neurona! Uspešno ste sakupili 1000 neurona!');
  }

  Future<void> pointHoarder() async {
    int neurons = 500;
    await Future.wait([
      _updateNeurons(
        neurons,
      ),
      _updateLeaderboard(neurons),
      _updateAchievements('point_hoarder')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a12', 'Čuvar neurona! Uspešno ste sakupili 5000 neurona!');
  }

  // Future<void> bronzeLeagueAchiever() async {
  //   // int neurons = 300;
  //   // await Future.wait([
  //   //   _updateNeurons(neurons),
  //   //   _updateLeaderboard(neurons),
  //   //   _updateAchievements('bronze_league_achiever')
  //   // ]);

  //   // // Show the dialog after updates are completed
  //   // Get.dialog(const AchievementDialog(
  //   //   image: 'a13',
  //   //   message: 'Dostigli ste u Bronzanu ligu!',
  //   // ));
  // }

  Future<void> silverLeagueAchiever() async {
    int neurons = 500;
    await Future.wait([
      _updateNeurons(
        neurons,
      ),
      _updateLeaderboard(neurons),
      _updateAchievements('silver_league_achiever')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a14', 'Dostigli ste u Srebrnu ligu!');
  }

  Future<void> goldLeagueAchiever() async {
    int neurons = 1000;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('gold_league_achiever')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a15', 'Dostigli ste u Zlatnu ligu!');
  }

  Future<void> quizMarathoner() async {
    int neurons = 400;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('quiz_marathoner')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a16', 'Maratonac kvizova! Završili ste 50 kvizova!');
  }

  Future<void> topicEnthusiast() async {
    int neurons = 200;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('topic_enthusiast')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a17', 'Entuzijasta za teme! Pročitali ste 10 različitih tema.');
  }

  Future<void> contentConnoisseur() async {
    int neurons = 300;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('content_connoisseur')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a18', 'Poznavalac sadržaja! Pročitali ste 20 različitih tema.');
  }

  Future<void> streakChampion() async {
    int neurons = 500;
    await Future.wait([
      _updateNeurons(
        neurons,
      ),
      _updateLeaderboard(neurons),
      _updateAchievements('streak_champion')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a19', 'Šampion niza! Održali ste niz od 30 uzastopnih dana.');
  }

  Future<void> classicQuizMaster() async {
    int neurons = 250;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('classic_quiz_master')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a21', 'Majstor kvizova! Završili ste 20 kvizova!');
  }

  Future<void> articleEnthusiast() async {
    int neurons = 400;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('article_enthusiast')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a22', 'Entuzijasta za članke! Pročitali ste 50 članaka.');
  }

  Future<void> quizVeteran() async {
    int neurons = 600;
    await Future.wait([
      _updateNeurons(neurons),
      _updateAchievements('quiz_veteran'),
      _updateLeaderboard(neurons)
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a23', 'Veteran kvizova! Završili ste 100 kvizova.');
  }

  Future<void> learningAddict() async {
    int neurons = 800;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('learning_addict')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a24', 'Ovisnik o učenju: Prijavili ste se uzastopno 60 dana!');
  }

  Future<void> quizWhiz() async {
    int neurons = 100;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('quiz_whiz')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a26', 'Kviz guru! Svi 100% tačni odgovori u kvizu!');
  }

  Future<void> historyBuff() async {
    int neurons = 300;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('history_buff')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a27',
        'Ljubitelj storije! Završili te 10 kvizova u kategoriji Istorija.');
  }

  Future<void> mythologyMaster() async {
    int neurons = 300;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('mythology_master')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a28',
        'Majstor mitologije! Završili te 10 kvizova u kategoriji Mitologija.');
  }

  Future<void> cosmosConqueror() async {
    int neurons = 300;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('cosmos_conqueror')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a29',
        'Osvojitelj Kosmosa! Završili te 10 kvizova u kategoriji Kosmos.');
  }

  Future<void> scienceSage() async {
    int neurons = 300;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('science_sage')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a30', 'Mudrac nauke! Završili te 10 kvizova u kategoriji Nauka.');
  }

  Future<void> artAficionado() async {
    int neurons = 300;
    await Future.wait([
      _updateNeurons(neurons),
      _updateAchievements('art_aficionado'),
      _updateLeaderboard(neurons)
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a31',
        'Aficionado umetnosti! Završili te 10 kvizova u kategoriji Umetnost.');
  }

  Future<void> marathoner() async {
    int neurons = 250;
    await Future.wait([
      _updateNeurons(neurons),
      _updateAchievements('marathoner'),
      _updateLeaderboard(neurons)
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a32', 'Maratonac! Čitate članke 30 minuta u jednoj sesiji.');
  }

  Future<void> knowledgeSeeker() async {
    int neurons = 800;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('knowledge_seeker')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a33', 'Tragač znanja! Odgovorili ste tačno na 100 pitanja na kvizu.');
  }

  Future<void> quizKingQueen() async {
    int neurons = 300;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('quiz_king_queen')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a34',
        'Kralj/Kraljica kvizova! Osvojili ste prvo mesto u svojoj ligi.');
  }

  Future<void> participationTrophy() async {
    int neurons = 1000;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('participation_trophy')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a35', 'Trofej za učešće! Učestvujte u 10 ligaških adogađaja.');
  }

  Future<void> competitiveSpirit() async {
    int neurons = 500;
    await Future.wait([
      _updateNeurons(
        neurons,
      ),
      _updateLeaderboard(neurons),
      _updateAchievements('competitive_spirit')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a36', 'Takmičarski duh! Osvojili ste 5 promocija u ligama.');
  }

  Future<void> consistentLearner() async {
    int neurons = 200;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('consistent_learner')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a38',
        'Dosledni učenik! Prijavili ste se i završili bar jedan kviz svakodnevno, 14 dana.');
  }

  Future<void> multiCategoryMaster() async {
    int neurons = 800;
    await Future.wait([
      _updateNeurons(neurons),
      _updateAchievements('multi_category_master'),
      _updateLeaderboard(neurons)
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a39',
        'Majstor u više kategorija! Završili ste kvizove u bar 5 kategorija.');
  }

  Future<void> powerUser() async {
    int neurons = 1000;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('power_user')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a43',
        'Moćni korisnik! Proveli ste više od 1000 minuta u umniLab aplikaciji.');
  }

  Future<void> perfectionist() async {
    int neurons = 250;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('perfectionist')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('a47',
        'Perfekcionista! Tačno ste odgovorili na sva pitanja u 5 uzastopnih kvizova.');
  }

  Future<void> loyalUser() async {
    int neurons = 1000;
    await Future.wait([
      _updateNeurons(neurons),
      _updateLeaderboard(neurons),
      _updateAchievements('loyal_user')
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog(
        'a49', 'Verni korisnik! Održali ste niz od 90 dana.');
  }

  Future<void> ultimateLearner() async {
    int neurons = 3000;
    await Future.wait([
      _updateNeurons(neurons),
      _updateAchievements('ultimate_learner'),
      _updateLeaderboard(neurons)
    ]);

    // Show the dialog after updates are completed
    _showAchievementDialog('50',
        'Ultimativni naučnik! Osvojili ste svaki bedž u umniLab platformi!');
  }
}
